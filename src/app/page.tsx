"use client"

import { useState, useEffect, type KeyboardEvent } from "react"
import { useAuth } from "@/hooks/use-auth"
import AuthForm from "@/components/auth/auth-form"
import { AppHeader } from "@/components/gtd/app-header"
import { CaptureSection } from "@/components/gtd/capture-section"
import { ProcessingSection } from "@/components/gtd/processing-section"
import { ProcessedItemsSection } from "@/components/gtd/processed-items-section"

type ItemType = {
  id: string
  text: string
  category?: string
}

// Category configuration with colors
type CategoryConfig = {
  id: string
  name: string
  color: string
}

export default function GTDCaptureApp() {
  const { user, loading, signOut } = useAuth()

  // Define categories array
  const categories: CategoryConfig[] = [
    { id: "essential-actionable-now", name: "Essential Actionable Now", color: "bg-red-500 hover:bg-red-600" },
    { id: "actionable-now", name: "Actionable Now", color: "bg-green-500 hover:bg-green-600" },
    {
      id: "essential-not-actionable-now",
      name: "Essential Not Actionable Now",
      color: "bg-orange-500 hover:bg-orange-600",
    },
    { id: "not-actionable-now", name: "Not Actionable Now", color: "bg-yellow-500 hover:bg-yellow-600" },
    { id: "future", name: "Future", color: "bg-blue-500 hover:bg-blue-600" },
    { id: "already-done", name: "Already Done", color: "bg-gray-500 hover:bg-gray-600" },
  ]

  const [inputText, setInputText] = useState("")
  const [items, setItems] = useState<ItemType[]>([])
  const [processedItems, setProcessedItems] = useState<ItemType[]>([])
  const [selectedItemIndex, setSelectedItemIndex] = useState<number | null>(null)
  const [sortByCategory, setSortByCategory] = useState(false)

  // Store items from the textarea
  const handleStore = () => {
    if (!inputText.trim()) return

    const newItems: ItemType[] = []

    // Extract chunks between CHUNKSTART and CHUNKEND as single items
    const chunkRegex = /CHUNKSTART([\s\S]*?)CHUNKEND/g
    let match
    let processedText = inputText

    while ((match = chunkRegex.exec(inputText)) !== null) {
      const chunkContent = match[1].trim()

      // Add the entire chunk content as a single item
      if (chunkContent) {
        newItems.push({
          id: crypto.randomUUID(),
          text: chunkContent,
        })
      }

      // Remove the processed chunk from the text
      processedText = processedText.replace(match[0], "")
    }

    // Process remaining text by newlines
    processedText
      .split("\n")
      .filter((text) => text.trim())
      .forEach((text) => {
        newItems.push({
          id: crypto.randomUUID(),
          text: text.trim(),
        })
      })

    setItems((prev) => [...prev, ...newItems])
    setInputText("")

    // Select the first item if nothing is selected
    if (selectedItemIndex === null && (items.length > 0 || newItems.length > 0)) {
      setSelectedItemIndex(0)
    }
  }

  // Process an item based on the category
  const processItem = (categoryId: string) => {
    if (selectedItemIndex === null || items.length === 0) return

    const itemToProcess = items[selectedItemIndex]
    const processedItem = { ...itemToProcess, category: categoryId }

    // Remove from items and add to processed items
    const newItems = items.filter((_, index) => index !== selectedItemIndex)
    setItems(newItems)
    setProcessedItems((prev) => [processedItem, ...prev])

    // Update selected index
    if (newItems.length > 0) {
      setSelectedItemIndex(Math.min(selectedItemIndex, newItems.length - 1))
    } else {
      setSelectedItemIndex(null)
    }
  }

  // Change category of a processed item
  const changeItemCategory = (itemId: string, newCategoryId: string) => {
    setProcessedItems((prev) => prev.map((item) => (item.id === itemId ? { ...item, category: newCategoryId } : item)))
  }

  // Handle keyboard shortcuts
  const handleKeyDown = (e: KeyboardEvent) => {
    if (selectedItemIndex === null) return

    // Check if the key is a number between 1 and the number of categories
    const keyNum = Number.parseInt(e.key)
    if (!isNaN(keyNum) && keyNum >= 1 && keyNum <= categories.length) {
      // Prevent the global handler from also processing this event
      e.preventDefault()
      e.stopPropagation()

      // Process the item with the category at index keyNum - 1
      processItem(categories[keyNum - 1].id)
    }
  }

  // Set up keyboard event listener
  useEffect(() => {
    const handleGlobalKeyDown = (e: globalThis.KeyboardEvent) => {
      // Only process keyboard shortcuts if not typing in the textarea
      // AND only if the event hasn't been handled by the table's own handler
      if (document.activeElement?.tagName !== "TEXTAREA" && !e.defaultPrevented) {
        const keyNum = Number.parseInt(e.key)
        if (!isNaN(keyNum) && keyNum >= 1 && keyNum <= categories.length) {
          e.preventDefault()

          // Use a setTimeout to ensure we're not processing the same keypress twice
          setTimeout(() => {
            if (selectedItemIndex !== null && items.length > 0) {
              processItem(categories[keyNum - 1].id)
            }
          }, 0)
        }
      }
    }

    window.addEventListener("keydown", handleGlobalKeyDown)
    return () => window.removeEventListener("keydown", handleGlobalKeyDown)
  }, [selectedItemIndex, items, categories])

  // Get sorted processed items
  const getSortedProcessedItems = () => {
    if (!sortByCategory) return processedItems

    // Create a copy to avoid mutating the original array
    return [...processedItems].sort((a, b) => {
      const catA = a.category || "uncategorized"
      const catB = b.category || "uncategorized"

      // Find the indices of the categories in the categories array
      const indexA = categories.findIndex((cat) => cat.id === catA)
      const indexB = categories.findIndex((cat) => cat.id === catB)

      // If both categories are found in the array, sort by their position
      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB
      }

      // If only one category is found, prioritize it
      if (indexA !== -1) return -1
      if (indexB !== -1) return 1

      // If neither is found, sort alphabetically
      return catA.localeCompare(catB)
    })
  }

  // Download processed items as a text file organized by category
  const downloadProcessedItems = () => {
    if (processedItems.length === 0) return

    // Organize items by category
    const categorizedItems: Record<string, string[]> = {}

    // Initialize categories
    categories.forEach((cat) => {
      categorizedItems[cat.id] = []
    })
    categorizedItems["uncategorized"] = []

    processedItems.forEach((item) => {
      const category = item.category || "uncategorized"
      if (!categorizedItems[category]) {
        categorizedItems[category] = []
      }
      categorizedItems[category].push(item.text)
    })

    // Create text content
    let textContent = "GTD Processed Items\n\n"

    Object.entries(categorizedItems).forEach(([categoryId, items]) => {
      if (items.length > 0) {
        const category = categories.find((cat) => cat.id === categoryId)
        const categoryName = category
          ? category.name
          : categoryId
              .split("-")
              .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
              .join(" ")

        textContent += `== ${categoryName} ==\n`
        items.forEach((item) => {
          textContent += `- ${item}\n`
        })
        textContent += "\n"
      }
    })

    // Create and download the file
    const blob = new Blob([textContent], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = "gtd-processed-items.txt"
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  // Show loading spinner while checking auth
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Show auth form if not authenticated
  if (!user) {
    return <AuthForm />
  }

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <AppHeader user={user} onSignOut={signOut} />

      <div className="grid gap-6">
        <CaptureSection inputText={inputText} onInputChange={setInputText} onStore={handleStore} />

        <ProcessingSection
          categories={categories}
          items={items}
          selectedItemIndex={selectedItemIndex}
          onItemSelect={setSelectedItemIndex}
          onKeyDown={handleKeyDown}
        />

        <ProcessedItemsSection
          items={getSortedProcessedItems()}
          categories={categories}
          sortByCategory={sortByCategory}
          onToggleSort={() => setSortByCategory(!sortByCategory)}
          onDownload={downloadProcessedItems}
          onCategoryChange={changeItemCategory}
        />
      </div>
    </div>
  )
}
