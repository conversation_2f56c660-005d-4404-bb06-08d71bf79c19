import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/captured-items - Get all captured items for a user
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json(
        { error: 'userId is required' },
        { status: 400 }
      )
    }

    const capturedItems = await prisma.capturedItem.findMany({
      where: {
        userId,
      },
      include: {
        tags: true,
        category: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    })

    return NextResponse.json(capturedItems)
  } catch (error) {
    console.error('Error fetching captured items:', error)
    return NextResponse.json(
      { error: 'Failed to fetch captured items' },
      { status: 500 }
    )
  }
}

// POST /api/captured-items - Create a new captured item
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, item, categoryId, tagNames } = body

    if (!userId || !item) {
      return NextResponse.json(
        { error: 'userId and item are required' },
        { status: 400 }
      )
    }

    const capturedItem = await prisma.capturedItem.create({
      data: {
        item,
        userId,
        categoryId: categoryId || null,
        tags: tagNames
          ? {
              create: tagNames.map((name: string) => ({ name })),
            }
          : undefined,
      },
      include: {
        tags: true,
        category: true,
      },
    })

    return NextResponse.json(capturedItem, { status: 201 })
  } catch (error) {
    console.error('Error creating captured item:', error)
    return NextResponse.json(
      { error: 'Failed to create captured item' },
      { status: 500 }
    )
  }
}
