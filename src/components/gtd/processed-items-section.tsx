"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Download, ArrowUpDown } from "lucide-react"
import { ProcessedItemsTable } from "./processed-items-table"

interface ItemType {
  id: string
  text: string
  category?: string
}

interface CategoryConfig {
  id: string
  name: string
  color: string
}

interface ProcessedItemsSectionProps {
  items: ItemType[]
  categories: CategoryConfig[]
  sortByCategory: boolean
  onToggleSort: () => void
  onDownload: () => void
  onCategoryChange: (itemId: string, newCategoryId: string) => void
}

export function ProcessedItemsSection({
  items,
  categories,
  sortByCategory,
  onToggleSort,
  onDownload,
  onCategoryChange,
}: ProcessedItemsSectionProps) {
  if (items.length === 0) return null

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Processed Items</CardTitle>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={onToggleSort} className="flex items-center gap-1">
            <ArrowUpDown size={16} />
            {sortByCategory ? "Unsort" : "Sort by Category"}
          </Button>
          <Button variant="outline" size="sm" onClick={onDownload} className="flex items-center gap-1">
            <Download size={16} />
            Download
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <ProcessedItemsTable items={items} categories={categories} onCategoryChange={onCategoryChange} />
      </CardContent>
    </Card>
  )
}
