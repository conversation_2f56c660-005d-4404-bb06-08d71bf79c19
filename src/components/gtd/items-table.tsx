"use client"

import { useRef, type KeyboardEvent } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface ItemType {
  id: string
  text: string
  category?: string
}

interface ItemsTableProps {
  items: ItemType[]
  selectedItemIndex: number | null
  onItemSelect: (index: number) => void
  onKeyDown: (e: KeyboardEvent) => void
}

export function ItemsTable({ items, selectedItemIndex, onItemSelect, onKeyDown }: ItemsTableProps) {
  const tableRef = useRef<HTMLDivElement>(null)

  return (
    <div ref={tableRef} className="border rounded-md" onKeyDown={onKeyDown} tabIndex={0}>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Items to Process ({items.length})</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {items.length === 0 ? (
            <TableRow>
              <TableCell className="text-center text-muted-foreground py-4">No items to process</TableCell>
            </TableRow>
          ) : (
            items.map((item, index) => (
              <TableRow
                key={item.id}
                className={selectedItemIndex === index ? "bg-muted" : ""}
                onClick={() => onItemSelect(index)}
              >
                <TableCell className="py-3">
                  {selectedItemIndex === index && (
                    <span className="inline-block w-2 h-2 bg-primary rounded-full mr-2"></span>
                  )}
                  {item.text}
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
