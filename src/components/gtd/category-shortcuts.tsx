"use client"

import { Badge } from "@/components/ui/badge"

interface CategoryConfig {
  id: string
  name: string
  color: string
}

interface CategoryShortcutsProps {
  categories: CategoryConfig[]
}

export function CategoryShortcuts({ categories }: CategoryShortcutsProps) {
  return (
    <div className="flex flex-wrap gap-2 mb-2">
      {categories.map((category, index) => (
        <Badge key={category.id} variant="outline" className="text-sm">
          Press {index + 1}: {category.name}
        </Badge>
      ))}
    </div>
  )
}
