"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { CategoryShortcuts } from "./category-shortcuts"
import { ItemsTable } from "./items-table"
import type { KeyboardEvent } from "react"

interface ItemType {
  id: string
  text: string
  category?: string
}

interface CategoryConfig {
  id: string
  name: string
  color: string
}

interface ProcessingSectionProps {
  categories: CategoryConfig[]
  items: ItemType[]
  selectedItemIndex: number | null
  onItemSelect: (index: number) => void
  onKeyDown: (e: KeyboardEvent) => void
}

export function ProcessingSection({
  categories,
  items,
  selectedItemIndex,
  onItemSelect,
  onKeyDown,
}: ProcessingSectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Process Items</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4">
          <CategoryShortcuts categories={categories} />
          <ItemsTable
            items={items}
            selectedItemIndex={selectedItemIndex}
            onItemSelect={onItemSelect}
            onKeyDown={onKeyDown}
          />
        </div>
      </CardContent>
    </Card>
  )
}
