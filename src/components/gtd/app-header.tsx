"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { LogOut } from "lucide-react"
import type { User } from "@supabase/supabase-js"

interface AppHeaderProps {
  user: User
  onSignOut: () => Promise<void>
}

export function AppHeader({ user, onSignOut }: AppHeaderProps) {
  return (
    <div className="flex justify-between items-center mb-6">
      <h1 className="text-3xl font-bold">GTD Capture Processing</h1>
      <div className="flex items-center gap-4">
        <span className="text-sm text-gray-600">Welcome, {user.email}</span>
        <Button variant="outline" size="sm" onClick={onSignOut} className="flex items-center gap-1">
          <LogOut size={16} />
          Sign Out
        </Button>
      </div>
    </div>
  )
}
