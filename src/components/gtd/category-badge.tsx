"use client"

import { Badge } from "@/components/ui/badge"

interface CategoryConfig {
  id: string
  name: string
  color: string
}

interface CategoryBadgeProps {
  categoryId?: string
  categories: CategoryConfig[]
}

export function CategoryBadge({ categoryId, categories }: CategoryBadgeProps) {
  if (!categoryId) return null

  const category = categories.find((cat) => cat.id === categoryId)
  if (!category) return <Badge>{categoryId}</Badge>

  return <Badge className={category.color}>{category.name}</Badge>
}
