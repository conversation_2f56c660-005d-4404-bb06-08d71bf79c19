"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface CaptureSectionProps {
  inputText: string
  onInputChange: (value: string) => void
  onStore: () => void
}

export function CaptureSection({ inputText, onInputChange, onStore }: CaptureSectionProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Capture Items</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4">
          <Textarea
            placeholder="Enter items separated by new lines..."
            value={inputText}
            onChange={(e) => onInputChange(e.target.value)}
            rows={5}
            className="resize-none"
          />
          <Button onClick={onStore}>Store Items</Button>
        </div>
      </CardContent>
    </Card>
  )
}
