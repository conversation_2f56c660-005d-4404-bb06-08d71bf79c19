"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CategoryBadge } from "./category-badge"

interface ItemType {
  id: string
  text: string
  category?: string
}

interface CategoryConfig {
  id: string
  name: string
  color: string
}

interface ProcessedItemsTableProps {
  items: ItemType[]
  categories: CategoryConfig[]
  onCategoryChange: (itemId: string, newCategoryId: string) => void
}

export function ProcessedItemsTable({ items, categories, onCategoryChange }: ProcessedItemsTableProps) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Item</TableHead>
          <TableHead className="w-[250px]">Category</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {items.map((item) => (
          <TableRow key={item.id}>
            <TableCell>{item.text}</TableCell>
            <TableCell>
              <div className="flex items-center gap-2">
                <CategoryBadge categoryId={item.category} categories={categories} />
                <Select value={item.category || ""} onValueChange={(value) => onCategoryChange(item.id, value)}>
                  <SelectTrigger className="w-[180px] h-8">
                    <SelectValue placeholder="Change category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
}
