import { prisma } from './prisma'

// Example functions for working with your GTD database

// User operations
export async function createUser(supabaseId: string, email?: string, name?: string) {
  return await prisma.user.create({
    data: {
      supabaseId,
      email,
      name,
    },
  })
}

export async function getUserBySupabaseId(supabaseId: string) {
  return await prisma.user.findUnique({
    where: {
      supabaseId,
    },
    include: {
      capturedItems: {
        include: {
          tags: true,
          category: true,
        },
      },
    },
  })
}

// Category operations
export async function createCategory(categoryName: string, order: number) {
  return await prisma.category.create({
    data: {
      categoryName,
      order,
    },
  })
}

export async function getAllCategories() {
  return await prisma.category.findMany({
    orderBy: {
      order: 'asc',
    },
  })
}

// CapturedItem operations
export async function createCapturedItem(
  userId: string,
  item: string,
  categoryId?: string,
  tagNames?: string[]
) {
  return await prisma.capturedItem.create({
    data: {
      item,
      userId,
      categoryId,
      tags: tagNames
        ? {
            create: tagNames.map((name) => ({ name })),
          }
        : undefined,
    },
    include: {
      tags: true,
      category: true,
      user: true,
    },
  })
}

export async function getCapturedItemsByUser(userId: string) {
  return await prisma.capturedItem.findMany({
    where: {
      userId,
    },
    include: {
      tags: true,
      category: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
  })
}

export async function updateCapturedItem(
  itemId: string,
  updates: {
    item?: string
    categoryId?: string
    tagNames?: string[]
  }
) {
  const { tagNames, ...otherUpdates } = updates

  return await prisma.capturedItem.update({
    where: {
      id: itemId,
    },
    data: {
      ...otherUpdates,
      ...(tagNames && {
        tags: {
          deleteMany: {}, // Remove all existing tags
          create: tagNames.map((name) => ({ name })), // Add new tags
        },
      }),
    },
    include: {
      tags: true,
      category: true,
    },
  })
}

export async function deleteCapturedItem(itemId: string) {
  return await prisma.capturedItem.delete({
    where: {
      id: itemId,
    },
  })
}

// Tag operations
export async function getTagsByItem(capturedItemId: string) {
  return await prisma.tag.findMany({
    where: {
      capturedItemId,
    },
  })
}

// Search operations
export async function searchCapturedItems(userId: string, searchTerm: string) {
  return await prisma.capturedItem.findMany({
    where: {
      userId,
      OR: [
        {
          item: {
            contains: searchTerm,
            mode: 'insensitive',
          },
        },
        {
          tags: {
            some: {
              name: {
                contains: searchTerm,
                mode: 'insensitive',
              },
            },
          },
        },
      ],
    },
    include: {
      tags: true,
      category: true,
    },
    orderBy: {
      createdAt: 'desc',
    },
  })
}
