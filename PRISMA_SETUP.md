# Prisma Setup for GTD App

This document explains how to use Prisma with your Supabase Postgres database.

## Database Schema

Your database includes the following tables:

### Users
- `id` - Primary key (CUID)
- `supabaseId` - Unique Supabase user ID
- `email` - User email (optional, unique)
- `name` - User name (optional)
- `createdAt` - Timestamp
- `updatedAt` - Timestamp

### Categories
- `id` - Primary key (CUID)
- `categoryName` - Category name
- `order` - Unique ordering number
- `createdAt` - Timestamp
- `updatedAt` - Timestamp

### CapturedItems
- `id` - Primary key (CUID)
- `item` - The captured item text
- `userId` - Foreign key to Users
- `categoryId` - Foreign key to Categories (optional)
- `createdAt` - Timestamp
- `updatedAt` - Timestamp

### Tags
- `id` - Primary key (CUID)
- `name` - Tag name
- `capturedItemId` - Foreign key to CapturedItems
- `createdAt` - Timestamp
- `updatedAt` - Timestamp

## Available Scripts

- `npm run db:generate` - Generate Prisma client
- `npm run db:migrate` - Create and apply new migration
- `npm run db:push` - Push schema changes without migration
- `npm run db:studio` - Open Prisma Studio (database GUI)
- `npm run db:reset` - Reset database and apply all migrations
- `npm run db:seed` - Seed database with default categories

## Usage Examples

### Import Prisma Client
```typescript
import { prisma } from '@/lib/prisma'
```

### Create a User
```typescript
const user = await prisma.user.create({
  data: {
    supabaseId: 'supabase-user-id',
    email: '<EMAIL>',
    name: 'John Doe',
  },
})
```

### Create a Captured Item with Tags
```typescript
const item = await prisma.capturedItem.create({
  data: {
    item: 'Buy groceries',
    userId: 'user-id',
    categoryId: 'category-id',
    tags: {
      create: [
        { name: 'shopping' },
        { name: 'urgent' },
      ],
    },
  },
  include: {
    tags: true,
    category: true,
  },
})
```

### Get User's Captured Items
```typescript
const items = await prisma.capturedItem.findMany({
  where: {
    userId: 'user-id',
  },
  include: {
    tags: true,
    category: true,
  },
  orderBy: {
    createdAt: 'desc',
  },
})
```

### Search Items
```typescript
const results = await prisma.capturedItem.findMany({
  where: {
    userId: 'user-id',
    OR: [
      {
        item: {
          contains: 'search-term',
          mode: 'insensitive',
        },
      },
      {
        tags: {
          some: {
            name: {
              contains: 'search-term',
              mode: 'insensitive',
            },
          },
        },
      },
    ],
  },
  include: {
    tags: true,
    category: true,
  },
})
```

## API Routes

### GET /api/captured-items?userId=xxx
Get all captured items for a user

### POST /api/captured-items
Create a new captured item
```json
{
  "userId": "user-id",
  "item": "Task description",
  "categoryId": "category-id",
  "tagNames": ["tag1", "tag2"]
}
```

### GET /api/categories
Get all categories

## Integration with Supabase Auth

When a user signs up through Supabase, create a corresponding user record:

```typescript
import { supabase } from '@/lib/supabase'
import { prisma } from '@/lib/prisma'

// After Supabase auth
const { data: { user } } = await supabase.auth.getUser()

if (user) {
  // Create or get user in Prisma
  const dbUser = await prisma.user.upsert({
    where: {
      supabaseId: user.id,
    },
    update: {},
    create: {
      supabaseId: user.id,
      email: user.email,
      name: user.user_metadata?.name,
    },
  })
}
```

## Default Categories

The seed script creates these default GTD categories:
1. Inbox
2. Next Actions
3. Projects
4. Waiting For
5. Someday/Maybe

## Environment Variables

Make sure your `.env` file includes:
```
DATABASE_URL=your-postgres-connection-string
```

## Schema Changes

When you modify the schema:
1. Update `prisma/schema.prisma`
2. Run `npm run db:migrate` to create and apply migration
3. Run `npm run db:generate` to update the Prisma client

## Prisma Studio

To view and edit your data visually:
```bash
npm run db:studio
```

This opens a web interface at http://localhost:5555
