import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create some default categories
  const categories = await Promise.all([
    prisma.category.upsert({
      where: { order: 1 },
      update: {},
      create: {
        categoryName: 'Inbox',
        order: 1,
      },
    }),
    prisma.category.upsert({
      where: { order: 2 },
      update: {},
      create: {
        categoryName: 'Next Actions',
        order: 2,
      },
    }),
    prisma.category.upsert({
      where: { order: 3 },
      update: {},
      create: {
        categoryName: 'Projects',
        order: 3,
      },
    }),
    prisma.category.upsert({
      where: { order: 4 },
      update: {},
      create: {
        categoryName: 'Waiting For',
        order: 4,
      },
    }),
    prisma.category.upsert({
      where: { order: 5 },
      update: {},
      create: {
        categoryName: 'Someday/Maybe',
        order: 5,
      },
    }),
  ])

  console.log('✅ Created categories:', categories.map(c => c.categoryName).join(', '))

  console.log('🎉 Seeding completed!')
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
