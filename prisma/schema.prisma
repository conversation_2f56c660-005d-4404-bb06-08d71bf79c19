generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String         @id @default(cuid())
  supabaseId    String         @unique @map("supabase_id")
  email         String?        @unique
  name          String?
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @updatedAt @map("updated_at")
  capturedItems CapturedItem[]

  @@map("users")
}

model Category {
  id            String         @id @default(cuid())
  categoryName  String         @map("category_name")
  order         Int            @unique
  createdAt     DateTime       @default(now()) @map("created_at")
  updatedAt     DateTime       @updatedAt @map("updated_at")
  capturedItems CapturedItem[]

  @@map("categories")
}

model CapturedItem {
  id         String    @id @default(cuid())
  item       String
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  userId     String    @map("user_id")
  categoryId String?   @map("category_id")
  category   Category? @relation(fields: [categoryId], references: [id])
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  tags       Tag[]

  @@map("captured_items")
}

model Tag {
  id             String       @id @default(cuid())
  name           String
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @updatedAt @map("updated_at")
  capturedItemId String       @map("captured_item_id")
  capturedItem   CapturedItem @relation(fields: [capturedItemId], references: [id], onDelete: Cascade)

  @@map("tags")
}
